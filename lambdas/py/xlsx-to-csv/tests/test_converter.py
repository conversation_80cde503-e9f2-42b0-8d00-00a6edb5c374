import os
import pandas as pd
import pytest
from src.converter import convert_xlsx_to_csv

@pytest.fixture
def sample_xlsx():
    # テスト用のExcelファイルを作成
    df = pd.DataFrame({
        'ID': [1, 2, 3],
        '名前': ['田中', '鈴木', '佐藤'],
        '年齢': [25, 30, 35]
    })

    file_path = 'test_sample.xlsx'
    df.to_excel(file_path, index=False)

    yield file_path

    # テスト後にファイルを削除
    if os.path.exists(file_path):
        os.remove(file_path)

    if os.path.exists('test_output.csv'):
        os.remove('test_output.csv')

def test_convert_xlsx_to_csv(sample_xlsx):
    # 変換処理を実行
    result = convert_xlsx_to_csv(sample_xlsx, 'test_output.csv')

    # 変換成功を確認
    assert result is True

    # 出力ファイルが存在することを確認
    assert os.path.exists('test_output.csv')

    # 内容が正しいことを確認
    df = pd.read_csv('test_output.csv')
    assert len(df) == 3
    assert '名前' in df.columns
    assert df['名前'][0] == '田中'
