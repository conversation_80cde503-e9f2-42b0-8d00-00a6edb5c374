// import { S3Client } from '@aws-sdk/client-s3';
import type { Handler } from 'aws-lambda';

import type { TenantInput } from 'models';

export type TransitEvent = {
  tenant: TenantInput;
  targetVersion: string;
};

export const handler: Handler<TransitEvent> = async (event) => {
  console.log(`\nEVENT: ${JSON.stringify(event, null, 2)}`);
  // const { tenant } = event;
  // const s3 = new S3Client();

  // まずはテナントIDを調べる
  // const envClient = createEnvClient();

  // 次にRLSクライアントを作成する
  // const rlsClient = createRlsClientProvider();

  // 以下、S3のデータを取得して処理するロジックを実装
};
