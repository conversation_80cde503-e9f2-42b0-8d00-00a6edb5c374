import * as path from 'node:path';
import * as cdk from 'aws-cdk-lib';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import type * as s3 from 'aws-cdk-lib/aws-s3';
import * as s3n from 'aws-cdk-lib/aws-s3-notifications';
import type { Construct } from 'constructs';

import { naming } from 'common';

import { rootDir } from '../../../../util/funcs';
import type { EnvStackProps } from '../../../../util/type';
import { setNotifier } from '../../utils/set-notifier';

type XlsxToCsvProps = EnvStackProps & {
  buckets: cdk.aws_s3.Bucket[];
};

/**
 * Excel(xlsx)ファイルをCSVに変換するLambda関数を作成
 */
export const createXlsxToCsv = (scope: Construct, props: XlsxToCsvProps): lambda.Function => {
  const { envKey, buckets } = props;
  const name = 'xlsx-to-csv';
  const functionName = naming(envKey, name);
  const policies: cdk.aws_iam.PolicyStatement[] = buckets.map(
    (bucket) =>
      new cdk.aws_iam.PolicyStatement({
        effect: cdk.aws_iam.Effect.ALLOW,
        resources: [`${bucket.bucketArn}/*`],
        actions: ['s3:GetObject', 's3:PutObject'],
      }),
  );

  // Lambda関数の作成
  const func = new lambda.Function(scope, `lambda-${name}`, {
    functionName,
    runtime: lambda.Runtime.PYTHON_3_13,
    handler: 'src.lambda_function.lambda_handler',
    code: lambda.Code.fromAsset(path.resolve(rootDir, 'lambdas/py/xlsx-to-csv')),
    memorySize: 512,
    timeout: cdk.Duration.seconds(60),
    initialPolicy: policies,
    environment: {
      ENV: envKey,
    },
    description: 'Excelファイル(xlsx)をCSV形式に変換するLambda関数',
  });
  // Python のエラーがサブスクリプションフィルターにマッチしない可能性あり。
  setNotifier(scope, name, { ...props, logGroupName: func.logGroup.logGroupName });
  return func;
};
