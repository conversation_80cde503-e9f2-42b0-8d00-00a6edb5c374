import { useParams } from '@tanstack/react-router';
import { useEffect } from 'react';

import { trpc } from '@/api';

import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { inquiryRouters } from '@/router/routes/inquiry';
import InquiryDetails from './InquiryDetails';

export default function InquiryDetailsPage() {
  const { id } = useParams({ from: '/app/inquiries/$id' });
  const utils = trpc.useUtils();

  const { data: inquiry } = trpc.inquiries.get.useQuery({ id });
  const { data: comments } = trpc.inquiries.comments.list.useQuery({ inquiryId: id });

  // マウントされたらキャッシュをinvalidateする
  useEffect(() => {
    utils.inquiries.get.invalidate({ id });
    utils.inquiries.comments.list.invalidate({ inquiryId: id });
  }, [id, utils]);

  const title = inquiryRouters.meta.details.useTitle();

  const content =
    inquiry === undefined || comments === undefined ? (
      <LoadingSpinner />
    ) : (
      <InquiryDetails key={id} inquiry={inquiry} comments={comments} />
    );

  return (
    <MainLayout scrollable back={{ to: '/inquiries' }} title={title}>
      {content}
    </MainLayout>
  );
}
